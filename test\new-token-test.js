// 使用最新token测试API
console.log('🚀 使用最新token测试API...')

// 解码并验证新token
function verifyNewToken() {
  console.log('\n=== 验证新JWT Token ===')

  const token = "eyJhbGciOiJIUzI1NiJ9.eyJzdWIiOiIyMXhkOXU3MCIsInVpZCI6MjgzMTM3LCJ1c2VyTm8iOiJDTjAyMjQzNDAiLCJhdXRoIjoxLCJleHAiOjE3NTQ0NzU5NDUsImlhdCI6MTc1NDQ2ODc0NX0.xfFfFXAZON1NTbM2P0Hb6pzYNcFL63RuC2ljy4HTSJQ"

  try {
    const payload = JSON.parse(atob(token.split('.')[1]))
    console.log('✅ JWT Token解码成功:')
    console.log('  用户ID:', payload.uid)
    console.log('  用户编号:', payload.userNo)
    console.log('  签发时间:', new Date(payload.iat * 1000).toLocaleString())
    console.log('  过期时间:', new Date(payload.exp * 1000).toLocaleString())

    const currentTime = Math.floor(Date.now() / 1000)
    const isValid = payload.exp > currentTime
    const timeLeft = payload.exp - currentTime

    console.log('  Token状态:', isValid ? '🟢 有效' : '🔴 已过期')
    if (isValid) {
      console.log('  剩余时间:', Math.floor(timeLeft / 3600), '小时', Math.floor((timeLeft % 3600) / 60), '分钟')
    }

    return { valid: isValid, token }
  } catch (error) {
    console.error('❌ JWT Token解码失败:', error.message)
    return { valid: false, error: error.message }
  }
}

// 测试站点API
async function testStationAPIWithNewToken(token) {
  console.log('\n=== 测试站点列表API ===')

  const API_URL = 'https://api-dm.usr.cn/dmCloud/dev/getDevs'

  // 使用您提供的完全相同的请求参数
  const requestBody = {
    appointLoraNodeDevice: 1,
    appointSubNodeDevice: 2,
    devicesTagParamDtos: [],
    pageNo: 1,
    pageSize: 10,
    projectId: "",
    searchParam: "",
    sortByWeight: "up",
    token: token
  }

  const headers = {
    'Content-Type': 'application/json',
    'Accept': '*/*',
    'Accept-Language': 'zh-CN,zh;q=0.9',
    'Origin': 'https://dm.usr.cn',
    'Referer': 'https://dm.usr.cn/',
    'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36',
    'appid': 'cG8yNDYwODl6a2xqYWdpdXNkZ3E=',
    'languagetype': '0',
    'token': token,
    'traceid': '283137',
    'u-source': 'in-pc',
    'sec-fetch-dest': 'empty',
    'sec-fetch-mode': 'cors',
    'sec-fetch-site': 'same-site'
  }

  try {
    console.log('📡 发送请求到:', API_URL)
    const response = await fetch(API_URL, {
      method: 'POST',
      headers: headers,
      body: JSON.stringify(requestBody)
    })

    console.log('📡 响应状态:', response.status)
    const result = await response.json()

    if (result.status === 0 && result.data) {
      console.log('🎉 ✅ API调用成功!')
      console.log('📊 总设备数量:', result.data.total)
      console.log('📋 当前页设备数:', result.data.list.length)

      if (result.data.list.length > 0) {
        console.log('\n📝 站点详细信息:')
        result.data.list.forEach((device, index) => {
          const status = device.deviceStatus.onlineOffline === 1 ? '🟢在线' : '🔴离线'
          console.log(`${index + 1}. ${device.deviceName || device.projectName}`)
          console.log(`   SN: ${device.sn}`)
          console.log(`   项目: ${device.projectName}`)
          console.log(`   型号: ${device.deviceModelName}`)
          console.log(`   状态: ${status}`)
          console.log(`   地址: ${device.deviceAddress || '未设置'}`)
          console.log('')
        })

        // 查找目标设备
        console.log('🎯 查找目标设备:')
        const targetDevices = [
          { sn: '02801925060700002997', name: '联丰村' },
        ]

        targetDevices.forEach(target => {
          const device = result.data.list.find(d => d.sn === target.sn)
          if (device) {
            console.log(`✅ 找到 ${target.name} (${target.sn}):`)
            console.log(`   实际名称: ${device.deviceName || device.projectName}`)
            console.log(`   在线状态: ${device.deviceStatus.onlineOffline === 1 ? '在线' : '离线'}`)
          } else {
            console.log(`❌ 未找到 ${target.name} (${target.sn})`)
          }
        })
      }

      return { success: true, data: result.data }
    } else {
      console.log('❌ API返回错误:')
      console.log('  状态码:', result.status)
      console.log('  错误信息:', result.info)
      return { success: false, error: result.info, status: result.status }
    }
  } catch (error) {
    console.error('❌ 请求异常:', error.message)
    return { success: false, error: error.message }
  }
}

// 主测试函数
async function runNewTokenTest() {
  try {
    console.log('开始测试最新token...')

    // 1. 验证token有效性
    const tokenCheck = verifyNewToken()

    if (!tokenCheck.valid) {
      console.log('\n💥 Token验证失败，无法继续测试')
      return
    }

    // 2. 测试API调用
    const apiResult = await testStationAPIWithNewToken(tokenCheck.token)

    // 3. 输出测试结果
    console.log('\n🎯 测试总结:')
    console.log('  Token状态: ✅ 有效')
    console.log('  API调用:', apiResult.success ? '✅ 成功' : '❌ 失败')

    if (apiResult.success) {
      console.log('\n🎉 恭喜! 外部API现在可以正常获取站点数据!')
      console.log('💡 建议: 应用现在应该可以显示真实的站点列表')

      // 输出可用于前端的数据预览
      if (apiResult.data.list.length > 0) {
        console.log('\n📋 前端将显示的站点:')
        apiResult.data.list.forEach(device => {
          console.log(`- ${device.deviceName || device.projectName} (${device.sn})`)
        })
      }
    } else {
      console.log('\n😞 API调用失败')
      console.log('原因:', apiResult.error)
      if (apiResult.status) {
        console.log('状态码:', apiResult.status)
      }
    }

  } catch (error) {
    console.error('💥 测试异常:', error)
  }
}

// 运行测试
runNewTokenTest()
