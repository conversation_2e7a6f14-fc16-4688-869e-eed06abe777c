import { TokenManager } from '../src/utils/tokenManager'
import { StationService } from '../src/services/stationService'

// 测试Token管理和站点API接口
async function testAPIs() {
  console.log('开始测试API接口...')

  try {
    // 测试Token管理
    console.log('\n=== 测试Token获取 ===')
    const token = await TokenManager.getToken()
    console.log('✅ Token获取成功:', token.substring(0, 20) + '...')

    // 测试获取站点列表
    console.log('\n=== 测试获取站点列表 ===')
    const stations = await StationService.getStationList()
    console.log('✅ 获取站点数量:', stations.length)
    
    if (stations.length > 0) {
      console.log('站点信息示例:')
      const station = stations[0]
      console.log({
        deviceName: station.deviceName,
        projectName: station.projectName,
        sn: station.sn,
        deviceModel: station.deviceModelName,
        isOnline: station.deviceStatus.onlineOffline === 1
      })
    }

    // 测试按项目分组
    console.log('\n=== 测试按项目分组 ===')
    const groupedStations = await StationService.getStationsByProject()
    console.log('✅ 项目分组数量:', groupedStations.size)
    
    for (const [projectName, projectStations] of groupedStations) {
      console.log(`项目 "${projectName}": ${projectStations.length} 个站点`)
    }

    // 测试获取在线设备
    console.log('\n=== 测试获取在线设备 ===')
    const onlineStations = await StationService.getOnlineStations()
    console.log('✅ 在线设备数量:', onlineStations.length)

    console.log('\n🎉 所有API测试完成!')

  } catch (error) {
    console.error('❌ API测试失败:', error)
    
    if (error instanceof Error) {
      console.error('错误详情:', error.message)
    }
  }
}

// 运行测试
testAPIs()