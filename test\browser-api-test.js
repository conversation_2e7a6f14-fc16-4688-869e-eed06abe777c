// 浏览器控制台API测试脚本
// 在浏览器开发者工具的Console中运行此代码

console.log('🌐 浏览器环境API测试开始...')

// 测试函数：获取站点列表
async function testStationAPIInBrowser() {
  console.log('\n=== 浏览器环境测试站点API ===')
  
  const API_URL = 'https://api-dm.usr.cn/dmCloud/dev/getDevs'
  
  // 使用您提供的最新token
  const token = "eyJhbGciOiJIUzI1NiJ9.eyJzdWIiOiIyMXhkOXU3MCIsInVpZCI6MjgzMTM3LCJ1c2VyTm8iOiJDTjAyMjQzNDAiLCJhdXRoIjoxLCJleHAiOjE3NTQzODA3MTcsImlhdCI6MTc1NDM3MzUxN30.Mgy06MJU0axukOXjs7Vqi9iXziwsAJi08qijOTRC0ls"
  
  const requestBody = {
    appointLoraNodeDevice: 1,
    appointSubNodeDevice: 2, 
    devicesTagParamDtos: [],
    pageNo: 1,
    pageSize: 10,
    projectId: "",
    searchParam: "",
    sortByWeight: "up",
    token: token
  }

  const headers = {
    'Content-Type': 'application/json',
    'Accept': '*/*',
    'Accept-Language': 'zh-CN,zh;q=0.9',
    'Origin': 'https://dm.usr.cn',
    'Referer': 'https://dm.usr.cn/',
    'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36',
    'appid': 'cG8yNDYwODl6a2xqYWdpdXNkZ3E=',
    'languagetype': '0',
    'token': token,
    'traceid': '283137',
    'u-source': 'in-pc',
    'sec-fetch-dest': 'empty',
    'sec-fetch-mode': 'cors',
    'sec-fetch-site': 'same-site'
  }

  try {
    console.log('📡 发送请求...')
    console.log('请求URL:', API_URL)
    console.log('请求体:', requestBody)
    
    const response = await fetch(API_URL, {
      method: 'POST',
      headers: headers,
      body: JSON.stringify(requestBody)
    })

    console.log('📡 响应状态:', response.status)
    console.log('📡 响应头:', Object.fromEntries([...response.headers]))
    
    const result = await response.json()
    console.log('📡 响应数据:', result)
    
    if (result.status === 0 && result.data) {
      console.log('✅ 成功获取站点数据!')
      console.log('总数量:', result.data.total)
      console.log('设备列表:', result.data.list)
      
      // 查找目标设备
      const targets = ['02801925060700002997', '02800125071500004977']
      targets.forEach(sn => {
        const device = result.data.list.find(d => d.sn === sn)
        if (device) {
          console.log(`✅ 找到设备 ${sn}:`, device)
        } else {
          console.log(`❌ 未找到设备 ${sn}`)
        }
      })
      
      return result.data
    } else {
      console.log('❌ API返回错误:')
      console.log('状态码:', result.status)  
      console.log('错误信息:', result.info)
      return null
    }
  } catch (error) {
    console.error('❌ 请求异常:', error)
    console.error('错误详情:', error.message)
    return null
  }
}

// 自动运行测试
testStationAPIInBrowser().then(data => {
  if (data) {
    console.log('\n🎉 测试成功! 外部API可以获取到站点信息')
    console.log('💡 建议: 更新应用中的token以启用真实数据获取')
  } else {
    console.log('\n😞 测试失败! 外部API暂时无法访问')
    console.log('💡 建议: 继续使用本地模拟数据')
  }
})

console.log('⏳ 请等待测试结果...')