# 任务调度界面优化报告

## 优化概述

根据提供的接口数据，对 `DeviceStatusView.vue` 中的任务调度界面进行了全面优化，主要改进了活动任务的显示和数据处理逻辑。

## 主要改进

### 1. 数据结构优化

#### 新增接口类型定义
```typescript
interface ActiveTaskItem {
  id: string;
  type: 'cycle' | 'sequence';
  device_name: string;
  status_text: string;
  remaining_time: string;
  next_action: string;
  device_sn: string;
  cycle_info?: {
    on_minutes: number;
    off_minutes: number;
  };
  sequence_info?: {
    device_a: string;
    device_b: string;
    do_a_minutes: number;
    do_b_minutes: number;
    healthy: boolean;
  };
}

interface ActiveTasksResponse {
  success: boolean;
  timestamp: string;
  active_tasks_count: number;
  active_tasks: ActiveTaskItem[];
}
```

#### 更新数据获取逻辑
- 修改 `fetchActiveTasksData()` 函数以正确处理新的接口响应格式
- 更新 `activeTasksData` 的类型定义，存储完整的响应数据

### 2. 界面显示优化

#### 任务摘要改进
- **循环任务摘要**: 使用 `/schedule/tasks/active` 接口数据，显示设备名称和时间配置
- **顺序任务摘要**: 显示设备A和设备B的轮换信息，包含运行时长
- **单次任务摘要**: 保持原有逻辑，通过 `/schedule/tasks` 接口获取

#### 活动任务列表增强
- **任务计数徽章**: 显示当前活动任务数量
- **任务类型标识**: 为不同类型任务添加颜色区分的标签
- **健康状态指示**: 显示任务异常状态（特别是顺序任务）
- **详细信息展示**: 
  - 剩余时间
  - 下一步操作
  - 任务健康状态警告

#### 新增界面元素
```vue
<h3 class="subsection-title">
  正在进行中的任务 
  <span class="task-count-badge">{{ activeTasksData?.active_tasks_count || 0 }}</span>
</h3>

<div class="task-header">
  <div class="task-device">{{ task.deviceName }}</div>
  <div class="task-type-badge" :class="getTaskTypeClass(task.type)">
    {{ task.type }}
  </div>
</div>

<div class="task-details">
  <div class="task-remaining" v-if="task.remainingTime">
    剩余时间: {{ task.remainingTime }}
  </div>
  <div class="task-next-action" v-if="task.nextAction && task.nextAction !== '未知'">
    下一步: {{ task.nextAction }}
  </div>
  <div class="task-health" v-if="task.isHealthy === false">
    <span class="health-warning">⚠️ 任务异常</span>
  </div>
</div>
```

### 3. 样式改进

#### 新增CSS样式
- **任务计数徽章**: 蓝色圆形徽章显示任务数量
- **任务类型标签**: 不同颜色区分循环、顺序、单次任务
- **健康状态指示**: 红色警告样式用于异常任务
- **任务详情布局**: 更清晰的信息层次结构

#### 样式类定义
```css
.task-count-badge {
  background-color: #007bff;
  color: white;
  font-size: 10px;
  font-weight: 600;
  padding: 2px 6px;
  border-radius: 10px;
}

.task-type-badge.task-cycle {
  background-color: #e3f2fd;
  color: #1976d2;
}

.task-type-badge.task-sequence {
  background-color: #e8f5e8;
  color: #388e3c;
}

.active-task-item.task-unhealthy {
  border-left-color: #dc3545;
  background: #fff5f5;
}
```

### 4. 功能增强

#### 数据处理优化
- **智能数据映射**: 将接口数据转换为界面友好的格式
- **健康状态检测**: 自动识别和显示任务异常状态
- **时间格式化**: 优化剩余时间的显示格式

#### 新增辅助函数
```typescript
function getTaskTypeClass(taskType: string): string {
  switch (taskType) {
    case '循环任务': return 'task-cycle'
    case '顺序任务': return 'task-sequence'
    case '单次任务': return 'task-single'
    default: return 'task-default'
  }
}

function getActiveTasksDisplay() {
  return activeTasksData.value.active_tasks.map(task => ({
    id: task.id,
    deviceName: task.device_name,
    statusText: task.status_text,
    remainingTime: task.remaining_time !== '运行中' ? task.remaining_time : undefined,
    type: task.type === 'cycle' ? '循环任务' : '顺序任务',
    nextAction: task.next_action,
    isHealthy: task.type === 'sequence' ? task.sequence_info?.healthy : true
  }))
}
```

## 接口数据利用

### 循环任务数据
- 使用 `cycle_info` 中的 `on_minutes` 和 `off_minutes`
- 显示设备名称和运行状态
- 展示剩余时间和下一步操作

### 顺序任务数据
- 利用 `sequence_info` 中的设备信息和时长配置
- 显示健康状态 (`healthy` 字段)
- 展示设备A和设备B的轮换信息

### 通用任务信息
- 任务ID、设备名称、状态文本
- 剩余时间和下一步操作
- 设备序列号信息

## 用户体验改进

1. **信息密度优化**: 在有限空间内展示更多有用信息
2. **视觉层次清晰**: 通过颜色和布局区分不同类型和状态
3. **状态一目了然**: 快速识别任务健康状态和剩余时间
4. **操作便捷性**: 保持原有的取消和修改功能

## 兼容性说明

- 保持了与现有API的完全兼容
- 单次任务仍使用原有的 `/schedule/tasks` 接口
- 循环和顺序任务优先使用新的 `/schedule/tasks/active` 接口
- 在接口数据不可用时提供降级显示

## 总结

此次优化充分利用了后端提供的丰富接口数据，显著提升了任务调度界面的信息展示能力和用户体验。界面现在能够：

- 实时显示任务数量和详细状态
- 清晰区分不同类型的任务
- 及时提醒用户任务异常情况
- 提供更准确的时间和操作信息

这些改进使得用户能够更好地监控和管理设备的自动化任务，提高了系统的可用性和可靠性。
