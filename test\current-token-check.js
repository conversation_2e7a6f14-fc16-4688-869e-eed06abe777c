// 检查当前JWT token的有效性并测试完整流程
console.log('🔍 检查当前JWT token有效性并测试完整流程...')

async function checkCurrentJWTToken() {
  console.log('\n=== 检查当前JWT Token ===')
  
  // 当前TokenManager中使用的JWT token
  const currentToken = "eyJhbGciOiJIUzI1NiJ9.eyJzdWIiOiIyMXhkOXU3MCIsInVpZCI6MjgzMTM3LCJ1c2VyTm8iOiJDTjAyMjQzNDAiLCJhdXRoIjoxLCJleHAiOjE3NTQ0NzU5NDUsImlhdCI6MTc1NDQ2ODc0NX0.xfFfFXAZON1NTbM2P0Hb6pzYNcFL63RuC2ljy4HTSJQ"
  
  try {
    // 解码JWT
    const payload = JSON.parse(atob(currentToken.split('.')[1]))
    console.log('✅ JWT解码成功:')
    console.log('  用户ID:', payload.uid)
    console.log('  签发时间:', new Date(payload.iat * 1000).toLocaleString())
    console.log('  过期时间:', new Date(payload.exp * 1000).toLocaleString())
    
    // 检查是否过期
    const currentTime = Math.floor(Date.now() / 1000)
    const isValid = payload.exp > currentTime
    const timeLeft = payload.exp - currentTime
    
    console.log('  当前时间:', new Date().toLocaleString())
    console.log('  Token状态:', isValid ? '🟢 有效' : '🔴 已过期')
    
    if (isValid) {
      console.log('  剩余时间:', Math.floor(timeLeft / 3600), '小时', Math.floor((timeLeft % 3600) / 60), '分钟')
    } else {
      console.log('  过期时间:', Math.abs(Math.floor(timeLeft / 3600)), '小时前')
    }
    
    return { valid: isValid, token: currentToken, payload }
    
  } catch (error) {
    console.error('❌ JWT解码失败:', error.message)
    return { valid: false, error: error.message }
  }
}

async function testCurrentTokenWithStationAPI(token) {
  console.log('\n=== 使用当前JWT token测试站点API ===')
  
  const API_URL = 'https://api-dm.usr.cn/dmCloud/dev/getDevs'
  
  const requestBody = {
    appointLoraNodeDevice: 1,
    appointSubNodeDevice: 2,
    devicesTagParamDtos: [],
    pageNo: 1,
    pageSize: 10,
    projectId: "",
    searchParam: "",
    sortByWeight: "up",
    token: token
  }

  const headers = {
    'Content-Type': 'application/json',
    'Accept': '*/*',
    'Accept-Language': 'zh-CN,zh;q=0.9',
    'Origin': 'https://dm.usr.cn',
    'Referer': 'https://dm.usr.cn/',
    'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36',
    'appid': 'cG8yNDYwODl6a2xqYWdpdXNkZ3E=',
    'languagetype': '0',
    'token': token,
    'traceid': '283137',
    'u-source': 'in-pc'
  }

  try {
    const response = await fetch(API_URL, {
      method: 'POST',
      headers: headers,
      body: JSON.stringify(requestBody)
    })

    const result = await response.json()
    
    if (result.status === 0 && result.data) {
      console.log('✅ 站点API调用成功!')
      console.log(`📊 获取到 ${result.data.list.length} 个站点`)
      
      // 显示站点信息
      result.data.list.forEach((device, index) => {
        console.log(`${index + 1}. ${device.deviceName || device.projectName}`)
        console.log(`   SN: ${device.sn}`)
        console.log(`   状态: ${device.deviceStatus.onlineOffline === 1 ? '🟢在线' : '🔴离线'}`)
      })
      
      return { success: true, data: result.data }
    } else {
      console.log('❌ 站点API调用失败:')
      console.log('  状态码:', result.status)
      console.log('  错误信息:', result.info)
      return { success: false, error: result.info, status: result.status }
    }
  } catch (error) {
    console.error('❌ 站点API异常:', error.message)
    return { success: false, error: error.message }
  }
}

// 主检查函数
async function runCurrentTokenCheck() {
  try {
    console.log('开始检查当前token状态...')
    
    // 1. 检查JWT token有效性
    const tokenCheck = await checkCurrentJWTToken()
    
    if (!tokenCheck.valid) {
      console.log('\n💥 当前JWT token无效或已过期')
      console.log('❌ 应用无法获取站点数据')
      console.log('💡 建议: 需要更新为有效的JWT token')
      return
    }
    
    // 2. 测试站点API调用
    const apiResult = await testCurrentTokenWithStationAPI(tokenCheck.token)
    
    // 3. 总结结果
    console.log('\n🎯 当前状态总结:')
    console.log('  JWT Token: ✅ 有效')
    console.log('  站点API:', apiResult.success ? '✅ 成功' : '❌ 失败')
    
    if (apiResult.success) {
      console.log('\n🎉 完美! 当前配置完全正常!')
      console.log('💡 应用现在可以正常获取真实站点数据')
      console.log(`📊 将显示 ${apiResult.data.list.length} 个站点`)
    } else {
      console.log('\n😞 当前token无法调用站点API')
      console.log('错误详情:', apiResult.error)
      console.log('💡 可能需要更新token或检查API权限')
    }
    
  } catch (error) {
    console.error('💥 检查异常:', error)
  }
}

// 运行检查
runCurrentTokenCheck()