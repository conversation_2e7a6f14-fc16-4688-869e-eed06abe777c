// 验证构建产物中的token是否正确
console.log('🔍 验证构建产物中的token...')

import fs from 'fs'
import path from 'path'
import { fileURLToPath } from 'url'

const __filename = fileURLToPath(import.meta.url)
const __dirname = path.dirname(__filename)

function findTokenInBuildFiles() {
  const distPath = path.join(__dirname, '../dist')
  
  try {
    // 查找JS文件
    const files = fs.readdirSync(path.join(distPath, 'assets'))
    const jsFiles = files.filter(file => file.endsWith('.js'))
    
    console.log('📦 构建文件:', jsFiles)
    
    for (const jsFile of jsFiles) {
      const filePath = path.join(distPath, 'assets', jsFile)
      const content = fs.readFileSync(filePath, 'utf8')
      
      // 查找JWT token模式
      const jwtPattern = /eyJ[A-Za-z0-9+\/=]*\.[A-Za-z0-9+\/=]*\.[A-Za-z0-9+\/=]*/g
      const tokens = content.match(jwtPattern) || []
      
      if (tokens.length > 0) {
        console.log(`\n📄 文件: ${jsFile}`)
        console.log('🔑 发现的token数量:', tokens.length)
        
        // 去重token
        const uniqueTokens = [...new Set(tokens)]
        
        uniqueTokens.forEach((token, index) => {
          console.log(`\nToken ${index + 1}:`)
          console.log('前20字符:', token.substring(0, 20) + '...')
          
          try {
            const payload = JSON.parse(atob(token.split('.')[1]))
            const expiresAt = new Date(payload.exp * 1000)
            const isValid = payload.exp > Math.floor(Date.now() / 1000)
            
            console.log('过期时间:', expiresAt.toLocaleString())
            console.log('状态:', isValid ? '🟢 有效' : '🔴 已过期')
            
            if (isValid) {
              console.log('✅ 构建产物使用的是有效token!')
            } else {
              console.log('❌ 构建产物使用的是过期token!')
            }
          } catch (error) {
            console.log('❌ Token解析失败:', error.message)
          }
        })
      }
    }
    
    return true
  } catch (error) {
    console.error('❌ 读取构建文件失败:', error.message)
    return false
  }
}

console.log('开始检查构建产物...')
const result = findTokenInBuildFiles()

if (result) {
  console.log('\n🎯 部署建议:')
  console.log('1. 将整个 dist/ 目录重新部署到服务器')
  console.log('2. 确保覆盖旧的文件')
  console.log('3. 清除浏览器缓存后测试')
  console.log('4. 验证站点列表API是否正常工作')
} else {
  console.log('\n⚠️ 无法验证构建产物，请手动检查')
}