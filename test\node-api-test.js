// Node.js环境下的API测试脚本
// 验证登录和获取设备列表接口

// 模拟fetch（Node.js环境）
const fetch = (...args) => import('node-fetch').then(({default: fetch}) => fetch(...args));

// 登录配置
const LOGIN_CONFIG = {
  url: 'https://accountapi.usr.cn/api/Login/loginByPassword',
  username: '***********',
  password: 'd33f11e1b1f613d4e295d77a23c04ef4',
  platformId: 'DmPub'
}

const DEVICES_URL = 'https://api-dm.usr.cn/dmCloud/dev/getDevs'

// 测试登录获取token
async function testLogin() {
  console.log('🔐 测试登录获取Token...')
  
  try {
    const response = await fetch(LOGIN_CONFIG.url, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({
        username: LOGIN_CONFIG.username,
        password: LOGIN_CONFIG.password,
        platformId: LOGIN_CONFIG.platformId
      })
    })

    const result = await response.json()
    
    if (result.status === 0 && result.data.token) {
      console.log('✅ 登录成功!')
      console.log('Account:', result.data.account)
      console.log('Company:', result.data.company)
      console.log('Token:', result.data.token.substring(0, 20) + '...')
      return result.data.token
    } else {
      console.error('❌ 登录失败:', result.info)
      return null
    }
  } catch (error) {
    console.error('❌ 登录请求异常:', error.message)
    return null
  }
}

// 测试获取设备列表
async function testGetDevices(token) {
  console.log('\n📱 测试获取设备列表...')
  
  if (!token) {
    console.error('❌ 没有有效的Token')
    return
  }

  // 尝试多种不同的请求方式
  const testCases = [
    {
      name: '方式1: 仅token在headers',
      headers: {
        'Content-Type': 'application/json',
        'token': token
      },
      body: '{}'
    },
    {
      name: '方式2: Authorization Bearer',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${token}`
      },
      body: '{}'
    },
    {
      name: '方式3: token作为查询参数',
      headers: {
        'Content-Type': 'application/json'
      },
      url: `${DEVICES_URL}?token=${token}`,
      body: '{}'
    },
    {
      name: '方式4: token在请求体中',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({ token: token })
    },
    {
      name: '方式5: GET请求with token header',
      method: 'GET',
      headers: {
        'Content-Type': 'application/json',
        'token': token
      }
    }
  ]

  for (const testCase of testCases) {
    console.log(`\n--- ${testCase.name} ---`)
    
    try {
      const url = testCase.url || DEVICES_URL
      const method = testCase.method || 'POST'
      
      const options = {
        method: method,
        headers: testCase.headers
      }
      
      if (method === 'POST' && testCase.body) {
        options.body = testCase.body
      }

      console.log('请求URL:', url)
      console.log('请求方法:', method)
      console.log('请求头:', testCase.headers)
      if (testCase.body) console.log('请求体:', testCase.body)

      const response = await fetch(url, options)
      const result = await response.json()
      
      console.log('响应状态:', response.status)
      console.log('响应结果:', JSON.stringify(result, null, 2))
      
      if (result.status === 0 && result.data) {
        console.log('✅ 成功! 设备总数:', result.data.total)
        return result // 成功后返回，不再尝试其他方式
      } else {
        console.log('❌ 失败:', result.info || '未知错误')
      }
    } catch (error) {
      console.error('❌ 请求异常:', error.message)
    }
    
    // 等待一秒再尝试下一种方式
    await new Promise(resolve => setTimeout(resolve, 1000))
  }
}

// 主测试函数
async function runTests() {
  console.log('🚀 开始API接口测试')
  console.log('=' * 50)
  
  const token = await testLogin()
  await testGetDevices(token)
  
  console.log('\n✨ 测试完成')
}

// 运行测试
runTests().catch(console.error)