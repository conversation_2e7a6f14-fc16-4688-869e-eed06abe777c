// 测试修复后的API调用
const fetch = (...args) => import('node-fetch').then(({default: fetch}) => fetch(...args));

// 使用网页端的JWT token和请求格式
const JWT_TOKEN = "eyJhbGciOiJIUzI1NiJ9.eyJzdWIiOiIyMXhkOXU3MCIsInVpZCI6MjgzMTM3LCJ1c2VyTm8iOiJDTjAyMjQzNDAiLCJhdXRoIjoxLCJleHAiOjE3NTQzODA3MTcsImlhdCI6MTc1NDM3MzUxN30.Mgy06MJU0axukOXjs7Vqi9iXziwsAJi08qijOTRC0ls"
const USER_ID = "283137"
const DEVICES_URL = 'https://api-dm.usr.cn/dmCloud/dev/getDevs'

async function testDeviceListAPI() {
  console.log('🚀 测试使用网页端格式的设备列表API')
  console.log('=' * 60)
  
  try {
    // 使用与网页端完全相同的请求格式
    const requestBody = {
      appointLoraNodeDevice: 1,
      appointSubNodeDevice: 2,
      devicesTagParamDtos: [],
      pageNo: 1,
      pageSize: 10,
      projectId: "",
      searchParam: "",
      sortByWeight: "up",
      token: JWT_TOKEN
    }

    const headers = {
      'Content-Type': 'application/json',
      'Accept': '*/*',
      'Accept-Language': 'zh-CN,zh;q=0.9',
      'Accept-Encoding': 'gzip, deflate, br, zstd',
      'Origin': 'https://dm.usr.cn',
      'Referer': 'https://dm.usr.cn/',
      'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
      'appid': 'cG8yNDYwODl6a2xqYWdpdXNkZ3E=',
      'languagetype': '0',
      'token': JWT_TOKEN,
      'traceid': USER_ID,
      'u-source': 'in-pc',
      'sec-fetch-dest': 'empty',
      'sec-fetch-mode': 'cors',
      'sec-fetch-site': 'same-site',
      'sec-ch-ua': '"Not)A;Brand";v="8", "Chromium";v="138", "Google Chrome";v="138"',
      'sec-ch-ua-mobile': '?0',
      'sec-ch-ua-platform': '"Windows"'
    }

    console.log('📤 发送请求...')
    console.log('URL:', DEVICES_URL)
    console.log('Method: POST')
    console.log('Headers:', JSON.stringify(headers, null, 2))
    console.log('Body:', JSON.stringify(requestBody, null, 2))

    const response = await fetch(DEVICES_URL, {
      method: 'POST',
      headers: headers,
      body: JSON.stringify(requestBody)
    })

    console.log('\n📥 响应信息:')
    console.log('状态码:', response.status)
    console.log('状态文本:', response.statusText)
    
    const result = await response.json()
    console.log('响应数据:', JSON.stringify(result, null, 2))

    if (result.status === 0 && result.data) {
      console.log('\n✅ 成功获取设备列表!')
      console.log('账户:', result.account)
      console.log('设备总数:', result.data.total)
      
      if (result.data.list && result.data.list.length > 0) {
        console.log('\n🎯 设备详情:')
        result.data.list.forEach((device, index) => {
          console.log(`\n${index + 1}. ${device.deviceName}`)
          console.log(`   项目: ${device.projectName}`)
          console.log(`   SN: ${device.sn}`)
          console.log(`   型号: ${device.deviceModelName}`)
          console.log(`   MAC: ${device.deviceMac}`)
          console.log(`   IMEI: ${device.deviceImei}`)
          console.log(`   状态: ${device.deviceStatus.onlineOffline === 1 ? '🟢 在线' : '🔴 离线'}`)
          console.log(`   固件版本: ${device.deviceStatus.version}`)
        })
      }
    } else {
      console.log('\n❌ 获取设备列表失败')
      console.log('错误信息:', result.info || '未知错误')
      console.log('状态码:', result.status)
    }

  } catch (error) {
    console.error('\n💥 请求异常:', error.message)
    if (error.stack) {
      console.error('错误堆栈:', error.stack)
    }
  }
}

// 运行测试
testDeviceListAPI()