# 水利智能管理平台 - 项目功能和架构文档

## 项目概述

水利智能管理平台是一个基于 Vue 3 的 H5 移动端应用，专为水利站的水泵和气泵设备监控管理而设计。该应用提供实时设备状态监控、手动设备控制、智能任务调度和历史数据查询等核心功能。

## 技术架构

### 前端技术栈
- **框架**: Vue 3.5.17 + TypeScript (Composition API)
- **构建工具**: Vite 7.0.0
- **状态管理**: Pinia 3.0.3 (轻量级状态管理)
- **路由**: Vue Router 4.5.1 (Hash 模式，兼容设备部署)
- **UI 组件**: Vant 4.9.21 (移动端UI组件库)
- **开发工具**: ESLint + Prettier + Vitest
- **HTTP 客户端**: node-fetch 3.3.2

### 项目结构
```
simple-h5/
├── src/
│   ├── App.vue                 # 根组件
│   ├── main.ts                 # 应用入口
│   ├── assets/                 # 静态资源
│   ├── components/             # 公共组件
│   ├── router/                 # 路由配置
│   │   └── index.ts           # 路由定义
│   ├── services/              # API服务层
│   │   └── stationService.ts  # 站点服务
│   ├── stores/                # 状态管理
│   │   └── counter.ts         # 计数器store
│   ├── types/                 # TypeScript类型定义
│   │   └── station.ts         # 站点相关类型
│   ├── utils/                 # 工具函数
│   │   └── tokenManager.ts    # Token管理
│   └── views/                 # 页面组件
│       ├── StationSelectView.vue    # 站点选择页
│       ├── DeviceStatusView.vue     # 设备状态页
│       └── HistoryView.vue          # 历史数据页
├── test/                      # 测试文件
├── public/                    # 公共资源
├── dist/                      # 构建输出
└── 配置文件...
```

## 核心功能模块

### 1. 站点选择模块 (StationSelectView.vue)

**主要功能**:
- 显示所有可用的水利站点列表
- 支持下拉刷新获取最新站点信息
- 显示站点在线/离线状态
- 支持API获取失败时的降级模拟数据

**技术特点**:
- 使用 Vant 的 van-cell-group 组件展示站点列表
- 集成 StationService 获取真实站点数据
- 实现了优雅的错误处理和降级机制
- 响应式设计，适配移动端

### 2. 设备状态监控模块 (DeviceStatusView.vue)

**主要功能**:
- **设备实时监控**: 4台设备(水泵1、水泵2、气泵1、气泵2)的运行状态和电流监测
- **旋钮状态显示**: 可视化显示设备当前档位(自动/停止/手动)
- **环境传感器**: 实时温湿度数据展示
- **浮球开关**: 水位传感器状态监控
- **电力参数**: 三相电压电流、总功率、用电度数等电能表数据
- **设备信息**: 设备SN、固件版本、IMEI、数据时间戳

**操作功能**:
- **DO直控**: 4路数字输出开关的手动控制(开启/关闭)
- **任务调度**: 支持三种类型的定时任务创建和管理
- **活动任务监控**: 实时显示当前运行的所有任务

**技术特点**:
- 30秒自动刷新机制保持数据实时性
- 复杂的状态计算和数据转换逻辑
- 丰富的用户交互和表单验证
- 卡片式布局设计，移动端优化

### 3. 任务调度系统

**任务类型**:

#### 单次任务 (Scheduled Tasks)
- **功能**: 延时执行一次性设备控制操作
- **参数**: 目标设备(DO21-DO24)、操作类型(开启/关闭)、延迟时间(分钟)
- **验证**: 自动检查设备是否处于自动模式

#### 循环任务 (Cyclic Tasks)  
- **功能**: 设备按设定周期自动开启和关闭
- **参数**: 目标设备、开启持续时间、关闭持续时间
- **特点**: 无限循环执行直到手动取消

#### 顺序任务 (Sequence Tasks)
- **功能**: 两个设备交替运行的循环模式
- **参数**: 设备A和设备B、各自运行时长
- **逻辑**: A运行→A停止+B启动→B停止+A启动→循环

#### 水泵配置管理
- **功能**: 水泵自动轮换时间配置
- **参数**: 水泵1运行多久后自动启动水泵2
- **默认**: 2小时轮换间隔

### 4. 历史数据查询模块 (HistoryView.vue)

**主要功能**:
- **多条件筛选**: 时间范围、设备状态、电流范围、关键字搜索
- **分页显示**: 支持大量历史数据的分页浏览
- **数据展示**: 卡片式展示历史记录详情
- **导航功能**: 页面跳转、上一页/下一页

**技术特点**:
- 可展开/收起的筛选面板
- 复杂的筛选条件组合
- 高效的数据分页处理
- 友好的空状态和错误状态处理

## API接口集成

### 后端服务
- **服务器地址**: `http://49.235.191.145:8500`
- **设备序列号**: `02801925060700002997`

### 核心接口
- `GET /data/{SN}`: 获取设备实时数据和传感器读数
- `POST /control/{SN}`: 执行DO开关控制命令
- `POST /schedule/task`: 创建单次延时任务
- `POST /schedule/cycle`: 创建循环任务
- `POST /schedule/sequence`: 创建顺序交替任务
- `GET /history/{SN}`: 查询历史数据(支持多种筛选参数)
- `GET /schedule/tasks`: 获取当前单次任务列表
- `GET /schedule/cycles`: 获取当前循环任务列表
- `GET /schedule/sequences`: 获取当前顺序任务列表
- `PUT /pump-config`: 更新水泵配置参数
- `GET /pump-config`: 获取当前水泵配置

### 外部API集成
- **USR云平台API**: `https://api-dm.usr.cn/dmCloud`
- **认证服务**: `https://accountapi.usr.cn/api/Login/loginByPassword`
- **Token管理**: JWT格式token的获取和管理

## 数据结构定义

### 设备状态数据
```typescript
interface DeviceStatus {
  deviceNo: string
  version: string
  onlineOffline: number
}

interface StationDevice {
  deviceNo: string
  projectId: number
  projectName: string
  deviceName: string
  deviceAddress: string
  deviceStatus: DeviceStatus
  // ... 更多字段
}
```

### 设备运行数据
```typescript
interface DeviceData {
  device_info: {
    sn: string
    imei: string
    fw_version: string
  }
  device_status: {
    auto_status: number
    stop_status: number
    manual_status: number
  }
  diannengbiao: {
    voltages: { Ua: number, Ub: number, Uc: number }
    currents: { Ia: number, Ib: number, Ic: number }
    active_power: { total: number }
    // ... 更多电力参数
  }
  wenshi: {
    temperature: number
    humidity: number
  }
  // ... 更多传感器数据
}
```

## 关键技术实现

### 1. Token管理系统
- JWT token的自动获取和刷新
- 本地存储管理
- 过期检测和自动续期

### 2. 状态管理
- 使用 Pinia 进行轻量级状态管理
- 响应式数据更新
- 组件间状态共享

### 3. 路由设计
- Hash模式路由，兼容各种部署环境
- 动态路由参数传递
- 路由守卫和权限控制

### 4. 用户体验优化
- 30秒自动刷新机制
- 智能表单验证
- 移动端响应式设计
- 状态色彩区分
- 完整的操作反馈

### 5. 错误处理
- API调用失败的降级处理
- 用户友好的错误提示
- 网络异常的重试机制

## 开发和部署

### 开发命令
```bash
npm install          # 安装依赖
npm run dev          # 开发服务器
npm run build        # 生产构建
npm run type-check   # 类型检查
npm run lint         # 代码检查
npm run test:unit    # 单元测试
npm run preview      # 预览构建
npm run format       # 代码格式化
```

### 构建配置
- Vite配置了相对路径部署
- 开发代理配置支持跨域API调用
- TypeScript严格模式
- ESLint和Prettier代码规范

### 部署特点
- 静态文件部署
- 支持任意路径部署
- 移动端优化
- 离线降级支持

## 项目特色

1. **专业的水利设备管理**: 针对水利站的特定需求设计
2. **实时监控能力**: 30秒刷新的实时数据展示
3. **智能任务调度**: 多种任务类型满足不同运行需求
4. **移动端优化**: 专为移动设备设计的用户界面
5. **稳定的API集成**: 完善的错误处理和降级机制
6. **现代化技术栈**: Vue 3 + TypeScript + Vite的现代开发体验

这个项目展现了现代前端技术在工业物联网领域的成功应用，为水利设备的智能化管理提供了完整的解决方案。
