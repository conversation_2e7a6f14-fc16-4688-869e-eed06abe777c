// 更详细的API测试，尝试各种可能的参数组合
const fetch = (...args) => import('node-fetch').then(({default: fetch}) => fetch(...args));

const LOGIN_CONFIG = {
  url: 'https://accountapi.usr.cn/api/Login/loginByPassword',
  username: '***********',
  password: 'd33f11e1b1f613d4e295d77a23c04ef4',
  platformId: 'DmPub'
}

const DEVICES_URL = 'https://api-dm.usr.cn/dmCloud/dev/getDevs'

// 获取登录信息（包含更多字段）
async function getLoginInfo() {
  console.log('🔐 获取完整登录信息...')
  
  try {
    const response = await fetch(LOGIN_CONFIG.url, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({
        username: LOGIN_CONFIG.username,
        password: LOGIN_CONFIG.password,
        platformId: LOGIN_CONFIG.platformId
      })
    })

    const result = await response.json()
    
    if (result.status === 0) {
      console.log('✅ 登录成功!')
      console.log('完整用户信息:', JSON.stringify(result.data, null, 2))
      return result.data
    } else {
      console.error('❌ 登录失败:', result.info)
      return null
    }
  } catch (error) {
    console.error('❌ 登录请求异常:', error.message)
    return null
  }
}

// 测试不同的token和参数组合
async function testDeviceAPI(loginData) {
  console.log('\n📱 测试设备API...')
  
  if (!loginData) {
    console.error('❌ 没有登录数据')
    return
  }

  const testCases = [
    {
      name: '使用account作为header',
      headers: {
        'Content-Type': 'application/json',
        'token': loginData.token,
        'account': loginData.account
      },
      body: JSON.stringify({})
    },
    {
      name: '使用platformId',
      headers: {
        'Content-Type': 'application/json',
        'token': loginData.token,
        'platformId': loginData.platformId
      },
      body: JSON.stringify({})
    },
    {
      name: '在body中传递account和token',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({
        token: loginData.token,
        account: loginData.account
      })
    },
    {
      name: '在body中传递完整登录信息',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({
        token: loginData.token,
        account: loginData.account,
        platformId: loginData.platformId,
        id: loginData.id
      })
    },
    {
      name: '使用User-Agent和其他常见headers',
      headers: {
        'Content-Type': 'application/json',
        'token': loginData.token,
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
        'Accept': 'application/json',
        'Accept-Language': 'zh-CN,zh;q=0.9'
      },
      body: JSON.stringify({})
    }
  ]

  for (const testCase of testCases) {
    console.log(`\n--- ${testCase.name} ---`)
    
    try {
      console.log('请求头:', testCase.headers)
      console.log('请求体:', testCase.body)

      const response = await fetch(DEVICES_URL, {
        method: 'POST',
        headers: testCase.headers,
        body: testCase.body
      })

      const result = await response.json()
      
      console.log('响应状态:', response.status)
      console.log('响应结果:', JSON.stringify(result, null, 2))
      
      if (result.status === 0 && result.data) {
        console.log('✅ 成功! 设备总数:', result.data.total)
        
        // 打印设备信息
        if (result.data.list && result.data.list.length > 0) {
          console.log('\n🎯 找到设备:')
          result.data.list.forEach((device, index) => {
            console.log(`${index + 1}. ${device.deviceName} (${device.projectName})`)
            console.log(`   SN: ${device.sn}`)
            console.log(`   型号: ${device.deviceModelName}`) 
            console.log(`   状态: ${device.deviceStatus.onlineOffline === 1 ? '在线' : '离线'}`)
            console.log(`   MAC: ${device.deviceMac}`)
            console.log(`   IMEI: ${device.deviceImei}`)
            console.log('')
          })
        }
        
        return result // 成功后返回，不再尝试其他方式
      } else {
        console.log('❌ 失败:', result.info || '未知错误')
      }
    } catch (error) {
      console.error('❌ 请求异常:', error.message)
    }
    
    // 等待一秒再尝试下一种方式
    await new Promise(resolve => setTimeout(resolve, 1500))
  }
}

// 主测试函数
async function runCompleteTest() {
  console.log('🚀 开始完整API接口测试')
  console.log('=' * 50)
  
  const loginData = await getLoginInfo()
  await testDeviceAPI(loginData)
  
  console.log('\n✨ 测试完成')
}

// 运行测试
runCompleteTest().catch(console.error)