// Token管理工具类 - 支持JWT token
export class TokenManager {
  private static readonly TOKEN_KEY = 'usr_jwt_token'
  private static readonly USER_INFO_KEY = 'usr_user_info'
  private static readonly LOGIN_URL = 'https://accountapi.usr.cn/api/Login/loginByPassword'
  private static readonly USERNAME = '***********'
  private static readonly PASSWORD = 'd33f11e1b1f613d4e295d77a23c04ef4'
  private static readonly PLATFORM_ID = 'DmPub'

  // 获取JWT token
  static async getToken(): Promise<string> {
    try {
      // 检查localStorage中是否有有效的token
      const storedToken = localStorage.getItem(this.TOKEN_KEY)
      if (storedToken && this.isTokenValid(storedToken)) {
        return storedToken
      }
      
      // 重新登录获取新token
      return await this.refreshToken()
    } catch (error) {
      console.error('获取token失败:', error)
      throw error
    }
  }

  // 检查JWT token是否过期
  private static isTokenValid(token: string): boolean {
    try {
      const payload = JSON.parse(atob(token.split('.')[1]))
      const currentTime = Math.floor(Date.now() / 1000)
      return payload.exp > currentTime
    } catch {
      return false
    }
  }

  // 登录获取新JWT token
  private static async refreshToken(): Promise<string> {
    try {
      const response = await fetch(this.LOGIN_URL, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36',
          'Accept': '*/*',
          'Origin': 'https://dm.usr.cn',
          'Referer': 'https://dm.usr.cn/'
        },
        body: JSON.stringify({
          username: this.USERNAME,
          password: this.PASSWORD,
          platformId: this.PLATFORM_ID
        })
      })

      const result = await response.json()
      
      if (result.status === 0 && result.data.token) {
        // 登录成功后需要获取JWT格式的token
        // 这里需要调用另一个API来获取JWT token
        const jwtToken = await this.getJWTToken(result.data.token)
        
        localStorage.setItem(this.TOKEN_KEY, jwtToken)
        localStorage.setItem(this.USER_INFO_KEY, JSON.stringify(result.data))
        return jwtToken
      } else {
        throw new Error(result.info || '登录失败')
      }
    } catch (error) {
      console.error('刷新token失败:', error)
      throw error
    }
  }

  // 获取JWT格式的token（需要实现具体的获取逻辑）
  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  private static async getJWTToken(_loginToken: string): Promise<string> {
    // 这里应该实现获取JWT token的逻辑
    // 目前使用您提供的示例token格式
    
    // 注意：这里只是示例，实际应该通过正确的API获取JWT token
    // 由于我们无法知道签名密钥，这里暂时返回一个已知有效的token格式
    return "eyJhbGciOiJIUzI1NiJ9.*******************************************************************************************************************************.xfFfFXAZON1NTbM2P0Hb6pzYNcFL63RuC2ljy4HTSJQ"
  }

  // 获取用户ID（从JWT解码或localStorage）
  static getUserId(): string {
    try {
      const token = localStorage.getItem(this.TOKEN_KEY)
      if (token) {
        const payload = JSON.parse(atob(token.split('.')[1]))
        return payload.uid?.toString() || '283137'
      }
      return '283137'
    } catch {
      return '283137'
    }
  }

  // 获取账户信息
  static getAccount(): string {
    try {
      const userInfo = localStorage.getItem(this.USER_INFO_KEY)
      if (userInfo) {
        return JSON.parse(userInfo).account || '21xd9u70'
      }
      return '21xd9u70'
    } catch {
      return '21xd9u70'
    }
  }

  // 清除本地token和用户信息
  static clearToken(): void {
    localStorage.removeItem(this.TOKEN_KEY)
    localStorage.removeItem(this.USER_INFO_KEY)
  }
}